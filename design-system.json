{"designSystem": {"name": "FlowUI Design System", "version": "1.0.0", "description": "现代化丝滑交互设计系统，专注于流畅的用户体验和视觉层次", "colorPalette": {"primary": {"gradient": "linear-gradient(135deg, #00d4ff 0%, #00ff88 100%)", "blue": "#00d4ff", "green": "#00ff88"}, "neutral": {"black": "#0a0a0a", "darkGray": "#1a1a1a", "mediumGray": "#a1a1aa", "lightGray": "#e5e5e5", "white": "#ffffff"}, "semantic": {"success": "#00ff88", "warning": "#ffbd2e", "error": "#ff5555", "info": "#00d4ff"}, "opacity": {"glass": "rgba(255, 255, 255, 0.05)", "glassBorder": "rgba(255, 255, 255, 0.1)", "glassHover": "rgba(255, 255, 255, 0.08)", "overlay": "rgba(10, 10, 10, 0.8)"}}, "typography": {"fontFamily": {"primary": "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif", "mono": "'SF Mono', Monaco, 'Cascadia Code', monospace"}, "fontWeights": {"light": 300, "regular": 400, "medium": 500, "semibold": 600, "bold": 700, "extrabold": 800, "black": 900}, "scale": {"xs": "12px", "sm": "14px", "base": "16px", "lg": "18px", "xl": "20px", "2xl": "24px", "3xl": "32px", "4xl": "36px", "5xl": "48px", "6xl": "56px", "7xl": "72px"}, "lineHeight": {"tight": 1.1, "normal": 1.4, "relaxed": 1.6}, "letterSpacing": {"tight": "-0.02em", "normal": "0", "wide": "0.05em"}}, "spacing": {"scale": {"xs": "4px", "sm": "8px", "md": "12px", "lg": "16px", "xl": "20px", "2xl": "24px", "3xl": "32px", "4xl": "40px", "5xl": "48px", "6xl": "60px", "7xl": "80px", "8xl": "120px"}, "containerMaxWidth": "1200px", "containerPadding": "24px"}, "borderRadius": {"sm": "6px", "md": "8px", "lg": "12px", "xl": "16px", "2xl": "20px", "full": "50%"}, "shadows": {"sm": "0 4px 8px rgba(0, 0, 0, 0.1)", "md": "0 8px 25px rgba(0, 0, 0, 0.15)", "lg": "0 20px 40px rgba(0, 0, 0, 0.3)", "glow": "0 8px 25px rgba(0, 212, 255, 0.3)"}, "animations": {"duration": {"fast": "0.2s", "normal": "0.3s", "slow": "0.6s", "slower": "0.8s"}, "easing": {"ease": "ease", "easeOut": "cubic-bezier(0.4, 0, 0.2, 1)", "easeIn": "cubic-bezier(0.4, 0, 1, 1)", "bounce": "cubic-bezier(0.25, 0.46, 0.45, 0.94)"}, "transforms": {"slideUp": "translateY(30px)", "slideDown": "translateY(-30px)", "slideLeft": "translateX(-30px)", "slideRight": "translateX(30px)", "scale": "scale(0.95)", "float": "translateY(-8px)"}}, "effects": {"glassmorphism": {"background": "rgba(255, 255, 255, 0.05)", "border": "1px solid rgba(255, 255, 255, 0.1)", "backdropFilter": "blur(20px)"}, "gradientText": {"background": "linear-gradient(135deg, #00d4ff 0%, #00ff88 100%)", "webkitBackgroundClip": "text", "webkitTextFillColor": "transparent", "backgroundClip": "text"}, "parallax": {"slow": "0.02", "medium": "0.05", "fast": "0.1"}}}, "components": {"button": {"primary": {"background": "linear-gradient(135deg, #00d4ff 0%, #00ff88 100%)", "color": "#0a0a0a", "padding": "12px 24px", "borderRadius": "8px", "fontWeight": 600, "transition": "all 0.2s ease", "hoverTransform": "translateY(-2px)", "hoverShadow": "0 8px 25px rgba(0, 212, 255, 0.3)"}, "secondary": {"background": "transparent", "color": "#ffffff", "border": "1px solid rgba(255, 255, 255, 0.2)", "padding": "12px 24px", "borderRadius": "8px", "fontWeight": 600, "transition": "all 0.2s ease", "hoverBackground": "rgba(255, 255, 255, 0.05)"}}, "card": {"base": {"background": "rgba(255, 255, 255, 0.05)", "border": "1px solid rgba(255, 255, 255, 0.1)", "borderRadius": "16px", "padding": "24px", "backdropFilter": "blur(20px)", "transition": "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)"}, "hover": {"background": "rgba(255, 255, 255, 0.08)", "transform": "translateY(-4px)", "borderColor": "rgba(255, 255, 255, 0.2)"}, "featured": {"borderColor": "rgba(0, 212, 255, 0.5)", "background": "rgba(0, 212, 255, 0.05)", "transform": "scale(1.05)"}}, "navbar": {"background": "rgba(10, 10, 10, 0.8)", "backdropFilter": "blur(20px)", "borderBottom": "1px solid rgba(255, 255, 255, 0.1)", "padding": "16px 24px", "position": "fixed", "zIndex": 1000, "scrolledBackground": "rgba(10, 10, 10, 0.95)"}, "hero": {"background": "radial-gradient(ellipse at center, rgba(0, 212, 255, 0.05) 0%, transparent 70%)", "minHeight": "100vh", "padding": "120px 0 80px"}}, "interactions": {"scrollAnimations": {"fadeInUp": {"initial": {"opacity": 0, "transform": "translateY(30px)"}, "animate": {"opacity": 1, "transform": "translateY(0)", "transition": "all 0.8s cubic-bezier(0.4, 0, 0.2, 1)"}}, "staggered": {"delay": "0.1s", "description": "为相邻元素添加递增延迟"}}, "parallax": {"floatingElements": {"speed": "0.05", "rotation": "0.005"}, "backgroundLayers": {"layer1": "0.02", "layer2": "0.05", "layer3": "0.1"}}, "hover": {"lift": "translateY(-8px)", "scale": "scale(1.02)", "glow": "0 20px 40px rgba(0, 0, 0, 0.3)"}}, "layout": {"grid": {"columns": {"mobile": "1fr", "tablet": "repeat(2, 1fr)", "desktop": "repeat(3, 1fr)"}, "gap": {"mobile": "20px", "tablet": "24px", "desktop": "32px"}}, "breakpoints": {"mobile": "480px", "tablet": "768px", "desktop": "1024px", "wide": "1200px"}}, "principles": {"performance": {"useTransform": "优先使用 transform 和 opacity 进行动画", "useRAF": "使用 requestAnimationFrame 优化滚动事件", "lazyLoad": "使用 IntersectionObserver 实现懒加载动画"}, "accessibility": {"semanticHTML": "使用语义化 HTML 结构", "focusStates": "为所有交互元素提供焦点状态", "colorContrast": "确保足够的颜色对比度"}, "userExperience": {"smoothScrolling": "实现平滑滚动体验", "progressiveFeedback": "提供渐进式视觉反馈", "consistentTiming": "保持一致的动画时序"}}}