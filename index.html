<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FlowUI - 现代化开发工具</title>
    <link rel="stylesheet" href="style.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <div class="logo-icon"></div>
                <span class="logo-text">FlowUI</span>
                <span class="preview-badge">PREVIEW</span>
            </div>
            <ul class="nav-menu">
                <li><a href="#features">功能</a></li>
                <li><a href="#pricing">定价</a></li>
                <li><a href="#docs">文档</a></li>
                <li><a href="#resources">资源</a></li>
            </ul>
            <button class="join-waitlist-btn">加入等待列表</button>
        </div>
    </nav>

    <!-- 英雄区域 -->
    <section id="hero" class="hero">
        <div class="hero-container">
            <div class="hero-content">
                <div class="hero-badge">
                    <span>介绍 FlowUI</span>
                </div>
                <h1 class="hero-title">
                    <span class="title-line">现代化的</span>
                    <span class="title-line gradient-text">开发体验</span>
                </h1>
                <p class="hero-subtitle">FlowUI 通过智能化的工作流程和直观的界面设计，帮助开发者构建更好的应用程序。</p>
                <div class="hero-actions">
                    <button class="cta-primary">加入等待列表</button>
                    <button class="cta-secondary">观看演示</button>
                </div>
            </div>
            <div class="hero-visual">
                <div class="code-window">
                    <div class="window-header">
                        <div class="window-controls">
                            <span class="control red"></span>
                            <span class="control yellow"></span>
                            <span class="control green"></span>
                        </div>
                        <div class="window-title">main.js</div>
                    </div>
                    <div class="code-content">
                        <div class="code-line"><span class="keyword">import</span> <span class="string">{ FlowUI }</span> <span class="keyword">from</span> <span class="string">'@flowui/core'</span></div>
                        <div class="code-line"></div>
                        <div class="code-line"><span class="keyword">const</span> <span class="variable">app</span> = <span class="keyword">new</span> <span class="function">FlowUI</span>()</div>
                        <div class="code-line"><span class="variable">app</span>.<span class="method">render</span>(<span class="string">'#app'</span>)</div>
                    </div>
                </div>
                <div class="floating-elements">
                    <div class="float-card card-1">
                        <div class="card-icon">⚡</div>
                        <div class="card-text">快速开发</div>
                    </div>
                    <div class="float-card card-2">
                        <div class="card-icon">🎨</div>
                        <div class="card-text">现代设计</div>
                    </div>
                    <div class="float-card card-3">
                        <div class="card-icon">🚀</div>
                        <div class="card-text">高性能</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 主要特性区域 -->
    <section class="main-feature">
        <div class="container">
            <div class="feature-content">
                <h2 class="feature-title">通过规范驱动开发驯服复杂性</h2>
                <p class="feature-description">FlowUI 将您的想法转化为清晰的需求、系统设计和具体的任务。</p>
                <div class="feature-steps">
                    <div class="step">
                        <div class="step-number">01</div>
                        <div class="step-content">
                            <h3>与 FlowUI 协作制定规范和架构</h3>
                        </div>
                    </div>
                    <div class="step">
                        <div class="step-number">02</div>
                        <div class="step-content">
                            <h3>智能代理实现规范，同时保持您的控制权</h3>
                        </div>
                    </div>
                </div>
            </div>
            <div class="feature-visual">
                <div class="task-board">
                    <div class="task-header">
                        <h4>项目任务</h4>
                        <div class="task-progress">
                            <div class="progress-bar">
                                <div class="progress-fill"></div>
                            </div>
                            <span>75% 完成</span>
                        </div>
                    </div>
                    <div class="task-list">
                        <div class="task-item completed">
                            <div class="task-checkbox checked"></div>
                            <span>设计系统架构</span>
                        </div>
                        <div class="task-item completed">
                            <div class="task-checkbox checked"></div>
                            <span>实现核心功能</span>
                        </div>
                        <div class="task-item in-progress">
                            <div class="task-checkbox progress"></div>
                            <span>编写单元测试</span>
                        </div>
                        <div class="task-item">
                            <div class="task-checkbox"></div>
                            <span>性能优化</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 自动化任务区域 -->
    <section class="automation-section">
        <div class="container">
            <div class="automation-content">
                <div class="automation-text">
                    <h2>通过智能钩子自动化任务</h2>
                    <p>将任务委托给在事件触发时自动执行的 AI 代理，如"文件保存"。</p>
                    <ul class="feature-list">
                        <li>代理根据预定义提示在后台自主执行</li>
                        <li>通过生成文档、单元测试或优化代码性能来扩展工作</li>
                        <li>智能工作流程管理</li>
                    </ul>
                </div>
                <div class="automation-visual">
                    <div class="workflow-diagram">
                        <div class="workflow-node trigger">
                            <div class="node-icon">📁</div>
                            <span>文件保存</span>
                        </div>
                        <div class="workflow-arrow">→</div>
                        <div class="workflow-node agent">
                            <div class="node-icon">🤖</div>
                            <span>AI 代理</span>
                        </div>
                        <div class="workflow-arrow">→</div>
                        <div class="workflow-node output">
                            <div class="node-icon">✨</div>
                            <span>自动优化</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 功能展示区域 -->
    <section class="features-showcase">
        <div class="container">
            <div class="showcase-header">
                <span class="section-badge">为什么选择 FlowUI</span>
                <h2 class="section-title">从想法编码到可行代码</h2>
            </div>

            <div class="showcase-grid">
                <div class="showcase-item">
                    <div class="showcase-image">
                        <div class="mock-interface">
                            <div class="interface-header">
                                <div class="header-tabs">
                                    <span class="tab active">任务列表</span>
                                    <span class="tab">代码审查</span>
                                </div>
                            </div>
                            <div class="interface-content">
                                <div class="task-row completed">✓ 实现用户认证</div>
                                <div class="task-row completed">✓ 设计数据库架构</div>
                                <div class="task-row in-progress">⟳ 构建 API 接口</div>
                                <div class="task-row pending">○ 编写测试用例</div>
                            </div>
                        </div>
                    </div>
                    <div class="showcase-text">
                        <h3>通过规范驱动开发为 AI 编码带来结构</h3>
                        <p>FlowUI 将您的提示转化为清晰的需求、结构化设计、经过强大测试验证的实现任务，以及由高级代理生成的代码。</p>
                    </div>
                </div>

                <div class="showcase-item reverse">
                    <div class="showcase-image">
                        <div class="mock-interface">
                            <div class="interface-header">
                                <div class="header-tabs">
                                    <span class="tab active">代码差异</span>
                                    <span class="tab">聊天</span>
                                </div>
                            </div>
                            <div class="interface-content">
                                <div class="diff-line added">+ const handleSubmit = async () => {</div>
                                <div class="diff-line added">+   await validateForm()</div>
                                <div class="diff-line removed">- // TODO: 实现表单提交</div>
                                <div class="diff-line added">+   submitForm()</div>
                                <div class="diff-line added">+ }</div>
                            </div>
                        </div>
                    </div>
                    <div class="showcase-text">
                        <h3>专为与代理协作而构建</h3>
                        <p>多模态聊天、规范驱动开发、代理钩子 - FlowUI 为您提供最佳工具，在熟悉的开发体验中提供全新的工作方式。</p>
                    </div>
                </div>

                <div class="showcase-item">
                    <div class="showcase-image">
                        <div class="mock-interface">
                            <div class="interface-header">
                                <div class="header-tabs">
                                    <span class="tab active">文件浏览器</span>
                                    <span class="tab">搜索</span>
                                </div>
                            </div>
                            <div class="interface-content">
                                <div class="file-tree">
                                    <div class="file-item">📁 src/</div>
                                    <div class="file-item indent">📄 App.js</div>
                                    <div class="file-item indent">📄 index.js</div>
                                    <div class="file-item">📁 components/</div>
                                    <div class="file-item indent">📄 Header.js</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="showcase-text">
                        <h3>更多上下文，更少重复</h3>
                        <p>通过规范、引导和智能上下文管理，FlowUI 理解您提示背后的意图，帮助您在更大的代码库上实现复杂功能。</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 用户评价区域 -->
    <section class="testimonials">
        <div class="container">
            <h2 class="section-title">受到全球开发者信赖</h2>
            <div class="testimonials-grid">
                <div class="testimonial-card">
                    <p>"FlowUI 的能力让我震惊。代理化体验真正具有变革性。从理解上下文的多模态输入到 IDE 内的完整生命周期控制，感觉就像在与一位高级开发者合作。"</p>
                    <div class="testimonial-author">
                        <div class="author-avatar"></div>
                        <div class="author-info">
                            <div class="author-name">张伟</div>
                            <div class="author-title">云工程负责人</div>
                        </div>
                    </div>
                </div>
                <div class="testimonial-card">
                    <p>"大多数工具擅长生成代码，但 FlowUI 在编写单行代码之前就为混乱带来了结构。"</p>
                    <div class="testimonial-author">
                        <div class="author-avatar"></div>
                        <div class="author-info">
                            <div class="author-name">李小明</div>
                            <div class="author-title">全栈开发工程师</div>
                        </div>
                    </div>
                </div>
                <div class="testimonial-card">
                    <p>"在大约两天内，我从零开始构建了一个安全的文件共享应用程序。只需与 FlowUI 分享我的需求，就能创建一个包含加密和各种安全编码实践的完全安全应用程序。"</p>
                    <div class="testimonial-author">
                        <div class="author-avatar"></div>
                        <div class="author-info">
                            <div class="author-name">王芳</div>
                            <div class="author-title">安全工程师</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 定价区域 -->
    <section id="pricing" class="pricing">
        <div class="container">
            <div class="pricing-header">
                <span class="section-badge">定价方案</span>
                <h2 class="section-title">选择适合您的方案</h2>
                <p class="pricing-subtitle">从个人开发者到企业团队，我们为每个人都准备了合适的方案</p>
            </div>

            <div class="pricing-toggle">
                <span class="toggle-label">月付</span>
                <div class="toggle-switch">
                    <input type="checkbox" id="pricing-toggle" class="toggle-input">
                    <label for="pricing-toggle" class="toggle-slider"></label>
                </div>
                <span class="toggle-label">年付 <span class="discount-badge">省20%</span></span>
            </div>

            <div class="pricing-grid">
                <!-- 免费方案 -->
                <div class="pricing-card free">
                    <div class="card-header">
                        <h3 class="plan-name">免费版</h3>
                        <div class="plan-price">
                            <span class="currency">¥</span>
                            <span class="amount">0</span>
                            <span class="period">/月</span>
                        </div>
                        <p class="plan-description">适合个人开发者和小型项目</p>
                    </div>
                    <div class="card-features">
                        <ul class="features-list">
                            <li class="feature-item">
                                <span class="feature-icon">✓</span>
                                <span>每月 100 次 AI 对话</span>
                            </li>
                            <li class="feature-item">
                                <span class="feature-icon">✓</span>
                                <span>基础代码生成</span>
                            </li>
                            <li class="feature-item">
                                <span class="feature-icon">✓</span>
                                <span>3 个项目</span>
                            </li>
                            <li class="feature-item">
                                <span class="feature-icon">✓</span>
                                <span>社区支持</span>
                            </li>
                            <li class="feature-item disabled">
                                <span class="feature-icon">✗</span>
                                <span>高级 AI 模型</span>
                            </li>
                            <li class="feature-item disabled">
                                <span class="feature-icon">✗</span>
                                <span>团队协作</span>
                            </li>
                        </ul>
                    </div>
                    <div class="card-action">
                        <button class="pricing-btn secondary">开始使用</button>
                    </div>
                </div>

                <!-- 专业版 -->
                <div class="pricing-card pro popular">
                    <div class="popular-badge">最受欢迎</div>
                    <div class="card-header">
                        <h3 class="plan-name">专业版</h3>
                        <div class="plan-price">
                            <span class="currency">¥</span>
                            <span class="amount monthly">99</span>
                            <span class="amount yearly" style="display: none;">79</span>
                            <span class="period monthly">/月</span>
                            <span class="period yearly" style="display: none;">/月</span>
                        </div>
                        <p class="plan-description">适合专业开发者和中型团队</p>
                    </div>
                    <div class="card-features">
                        <ul class="features-list">
                            <li class="feature-item">
                                <span class="feature-icon">✓</span>
                                <span>无限 AI 对话</span>
                            </li>
                            <li class="feature-item">
                                <span class="feature-icon">✓</span>
                                <span>高级代码生成</span>
                            </li>
                            <li class="feature-item">
                                <span class="feature-icon">✓</span>
                                <span>无限项目</span>
                            </li>
                            <li class="feature-item">
                                <span class="feature-icon">✓</span>
                                <span>Claude Sonnet 4 模型</span>
                            </li>
                            <li class="feature-item">
                                <span class="feature-icon">✓</span>
                                <span>代码审查和优化</span>
                            </li>
                            <li class="feature-item">
                                <span class="feature-icon">✓</span>
                                <span>优先技术支持</span>
                            </li>
                            <li class="feature-item">
                                <span class="feature-icon">✓</span>
                                <span>自定义工作流</span>
                            </li>
                        </ul>
                    </div>
                    <div class="card-action">
                        <button class="pricing-btn primary">立即升级</button>
                    </div>
                </div>

                <!-- 企业版 -->
                <div class="pricing-card enterprise">
                    <div class="card-header">
                        <h3 class="plan-name">企业版</h3>
                        <div class="plan-price">
                            <span class="currency">¥</span>
                            <span class="amount monthly">299</span>
                            <span class="amount yearly" style="display: none;">239</span>
                            <span class="period monthly">/月</span>
                            <span class="period yearly" style="display: none;">/月</span>
                        </div>
                        <p class="plan-description">适合大型团队和企业组织</p>
                    </div>
                    <div class="card-features">
                        <ul class="features-list">
                            <li class="feature-item">
                                <span class="feature-icon">✓</span>
                                <span>专业版所有功能</span>
                            </li>
                            <li class="feature-item">
                                <span class="feature-icon">✓</span>
                                <span>团队协作工具</span>
                            </li>
                            <li class="feature-item">
                                <span class="feature-icon">✓</span>
                                <span>企业级安全</span>
                            </li>
                            <li class="feature-item">
                                <span class="feature-icon">✓</span>
                                <span>SSO 单点登录</span>
                            </li>
                            <li class="feature-item">
                                <span class="feature-icon">✓</span>
                                <span>API 访问权限</span>
                            </li>
                            <li class="feature-item">
                                <span class="feature-icon">✓</span>
                                <span>专属客户经理</span>
                            </li>
                            <li class="feature-item">
                                <span class="feature-icon">✓</span>
                                <span>自定义集成</span>
                            </li>
                        </ul>
                    </div>
                    <div class="card-action">
                        <button class="pricing-btn secondary">联系销售</button>
                    </div>
                </div>
            </div>

            <!-- 常见问题 -->
            <div class="pricing-faq">
                <h3 class="faq-title">常见问题</h3>
                <div class="faq-grid">
                    <div class="faq-item">
                        <h4 class="faq-question">可以随时取消订阅吗？</h4>
                        <p class="faq-answer">当然可以。您可以随时在账户设置中取消订阅，取消后将在当前计费周期结束时生效。</p>
                    </div>
                    <div class="faq-item">
                        <h4 class="faq-question">支持哪些支付方式？</h4>
                        <p class="faq-answer">我们支持支付宝、微信支付、银行卡以及国际信用卡等多种支付方式。</p>
                    </div>
                    <div class="faq-item">
                        <h4 class="faq-question">企业版有什么特殊服务？</h4>
                        <p class="faq-answer">企业版提供专属客户经理、定制化集成服务、企业级安全保障和优先技术支持。</p>
                    </div>
                    <div class="faq-item">
                        <h4 class="faq-question">有免费试用期吗？</h4>
                        <p class="faq-answer">专业版和企业版都提供 14 天免费试用，无需信用卡，试用期结束后可选择继续订阅。</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA区域 -->
    <section class="final-cta">
        <div class="container">
            <div class="cta-content">
                <h2>几分钟内构建真实项目</h2>
                <p>FlowUI 在预览期间免费使用</p>
                <button class="cta-primary large">加入等待列表</button>
            </div>
        </div>
    </section>

    <!-- 页脚 -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-brand">
                    <div class="footer-logo">
                        <div class="logo-icon"></div>
                        <span class="logo-text">FlowUI</span>
                    </div>
                </div>
                <div class="footer-links">
                    <div class="link-group">
                        <h4>产品</h4>
                        <a href="#">关于 FlowUI</a>
                        <a href="#">定价</a>
                        <a href="#">更新日志</a>
                    </div>
                    <div class="link-group">
                        <h4>资源</h4>
                        <a href="#">文档</a>
                        <a href="#">博客</a>
                        <a href="#">常见问题</a>
                    </div>
                    <div class="link-group">
                        <h4>社交</h4>
                        <a href="#">Twitter</a>
                        <a href="#">GitHub</a>
                        <a href="#">Discord</a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- 滚动进度条 -->
    <div class="scroll-progress"></div>

    <script src="script.js"></script>
</body>
</html>
