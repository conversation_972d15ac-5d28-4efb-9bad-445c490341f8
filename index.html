<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>丝滑滚动体验 - 现代化UI</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <h2>SilkyUI</h2>
            </div>
            <ul class="nav-menu">
                <li><a href="#hero">首页</a></li>
                <li><a href="#features">特性</a></li>
                <li><a href="#showcase">展示</a></li>
                <li><a href="#contact">联系</a></li>
            </ul>
        </div>
    </nav>

    <!-- 英雄区域 -->
    <section id="hero" class="hero">
        <div class="hero-content">
            <h1 class="hero-title">
                <span class="title-line">丝滑如丝的</span>
                <span class="title-line">滚动体验</span>
            </h1>
            <p class="hero-subtitle">体验现代化的网页交互，每一次滚动都如丝般顺滑</p>
            <button class="cta-button">开始体验</button>
        </div>
        <div class="hero-visual">
            <div class="floating-card card-1"></div>
            <div class="floating-card card-2"></div>
            <div class="floating-card card-3"></div>
        </div>
    </section>

    <!-- 特性区域 -->
    <section id="features" class="features">
        <div class="container">
            <h2 class="section-title">为什么选择我们</h2>
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">🚀</div>
                    <h3>极速性能</h3>
                    <p>60fps的丝滑动画，硬件加速优化，让每一次交互都流畅无比</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">✨</div>
                    <h3>现代设计</h3>
                    <p>简洁优雅的设计语言，符合现代审美的视觉体验</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">📱</div>
                    <h3>响应式布局</h3>
                    <p>完美适配各种设备，从手机到桌面都有最佳体验</p>
                </div>
            </div>
        </div>
    </section>

    <!-- 展示区域 -->
    <section id="showcase" class="showcase">
        <div class="container">
            <div class="showcase-content">
                <div class="showcase-text">
                    <h2>视差滚动效果</h2>
                    <p>当你滚动页面时，不同的元素以不同的速度移动，创造出深度感和层次感。这种效果让页面更加生动有趣。</p>
                    <ul>
                        <li>✓ 多层视差效果</li>
                        <li>✓ 平滑的过渡动画</li>
                        <li>✓ 优化的性能表现</li>
                    </ul>
                </div>
                <div class="showcase-visual">
                    <div class="parallax-container">
                        <div class="parallax-layer layer-1"></div>
                        <div class="parallax-layer layer-2"></div>
                        <div class="parallax-layer layer-3"></div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 滚动触发动画区域 -->
    <section class="scroll-animations">
        <div class="container">
            <h2 class="section-title">滚动触发动画</h2>
            <div class="animation-grid">
                <div class="anim-item" data-animation="fadeInUp">
                    <h3>淡入上升</h3>
                    <p>元素从下方淡入并上升</p>
                </div>
                <div class="anim-item" data-animation="fadeInLeft">
                    <h3>左侧滑入</h3>
                    <p>元素从左侧滑入视图</p>
                </div>
                <div class="anim-item" data-animation="fadeInRight">
                    <h3>右侧滑入</h3>
                    <p>元素从右侧滑入视图</p>
                </div>
                <div class="anim-item" data-animation="scaleIn">
                    <h3>缩放进入</h3>
                    <p>元素从小到大缩放进入</p>
                </div>
            </div>
        </div>
    </section>

    <!-- 联系区域 -->
    <section id="contact" class="contact">
        <div class="container">
            <h2 class="section-title">联系我们</h2>
            <p class="contact-subtitle">体验更多丝滑的交互效果</p>
            <button class="cta-button">立即联系</button>
        </div>
    </section>

    <!-- 滚动进度条 -->
    <div class="scroll-progress"></div>

    <script src="script.js"></script>
</body>
</html>
