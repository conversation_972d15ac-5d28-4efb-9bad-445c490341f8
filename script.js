// 丝滑滚动效果实现
class SilkyScroll {
    constructor() {
        this.init();
        this.setupScrollProgress();
        this.setupParallax();
        this.setupScrollAnimations();
        this.setupSmoothScrolling();
    }

    init() {
        // 等待DOM加载完成
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.start());
        } else {
            this.start();
        }
    }

    start() {
        console.log('🚀 丝滑滚动效果已启动');
        this.observeElements();
    }

    // 滚动进度条
    setupScrollProgress() {
        const progressBar = document.querySelector('.scroll-progress');
        
        window.addEventListener('scroll', () => {
            const scrollTop = window.pageYOffset;
            const docHeight = document.documentElement.scrollHeight - window.innerHeight;
            const scrollPercent = (scrollTop / docHeight) * 100;
            
            progressBar.style.width = scrollPercent + '%';
        });
    }

    // 视差滚动效果
    setupParallax() {
        const parallaxLayers = document.querySelectorAll('.parallax-layer');
        const floatingCards = document.querySelectorAll('.floating-card');

        window.addEventListener('scroll', () => {
            const scrolled = window.pageYOffset;
            const rate = scrolled * -0.5;

            // 视差层效果
            parallaxLayers.forEach((layer, index) => {
                const speed = (index + 1) * 0.3;
                layer.style.transform = `translateY(${scrolled * speed}px)`;
            });

            // 浮动卡片视差
            floatingCards.forEach((card, index) => {
                const speed = 0.1 + (index * 0.05);
                card.style.transform = `translateY(${scrolled * speed}px) rotate(${scrolled * 0.01}deg)`;
            });
        });
    }

    // 滚动触发动画
    setupScrollAnimations() {
        const animItems = document.querySelectorAll('.anim-item');
        const featureCards = document.querySelectorAll('.feature-card');

        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const element = entry.target;
                    const animationType = element.dataset.animation;
                    
                    // 添加动画类
                    element.style.opacity = '1';
                    element.style.transform = 'translateY(0)';
                    element.style.transition = 'all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
                    
                    // 根据动画类型应用不同效果
                    this.applyAnimation(element, animationType);
                }
            });
        }, observerOptions);

        // 观察动画元素
        [...animItems, ...featureCards].forEach(item => {
            observer.observe(item);
        });
    }

    // 应用不同的动画效果
    applyAnimation(element, type) {
        switch(type) {
            case 'fadeInLeft':
                element.style.transform = 'translateX(-50px)';
                setTimeout(() => {
                    element.style.transform = 'translateX(0)';
                }, 100);
                break;
            case 'fadeInRight':
                element.style.transform = 'translateX(50px)';
                setTimeout(() => {
                    element.style.transform = 'translateX(0)';
                }, 100);
                break;
            case 'scaleIn':
                element.style.transform = 'scale(0.8)';
                setTimeout(() => {
                    element.style.transform = 'scale(1)';
                }, 100);
                break;
            default:
                // fadeInUp 默认效果
                break;
        }
    }

    // 平滑滚动到锚点
    setupSmoothScrolling() {
        const navLinks = document.querySelectorAll('.nav-menu a[href^="#"]');
        
        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const targetId = link.getAttribute('href');
                const targetElement = document.querySelector(targetId);
                
                if (targetElement) {
                    const offsetTop = targetElement.offsetTop - 80; // 考虑导航栏高度
                    
                    window.scrollTo({
                        top: offsetTop,
                        behavior: 'smooth'
                    });
                }
            });
        });
    }

    // 观察元素进入视口
    observeElements() {
        const elements = document.querySelectorAll('.feature-card, .anim-item');
        
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-in');
                }
            });
        }, {
            threshold: 0.1
        });

        elements.forEach(el => observer.observe(el));
    }
}

// 鼠标跟随效果
class MouseFollower {
    constructor() {
        this.cursor = this.createCursor();
        this.setupMouseEvents();
    }

    createCursor() {
        const cursor = document.createElement('div');
        cursor.className = 'custom-cursor';
        cursor.style.cssText = `
            position: fixed;
            width: 20px;
            height: 20px;
            background: linear-gradient(45deg, #00ff88, #00ccff);
            border-radius: 50%;
            pointer-events: none;
            z-index: 9999;
            transition: transform 0.1s ease;
            opacity: 0;
        `;
        document.body.appendChild(cursor);
        return cursor;
    }

    setupMouseEvents() {
        document.addEventListener('mousemove', (e) => {
            this.cursor.style.left = e.clientX - 10 + 'px';
            this.cursor.style.top = e.clientY - 10 + 'px';
            this.cursor.style.opacity = '0.8';
        });

        document.addEventListener('mouseenter', () => {
            this.cursor.style.opacity = '0.8';
        });

        document.addEventListener('mouseleave', () => {
            this.cursor.style.opacity = '0';
        });

        // 悬停效果
        const interactiveElements = document.querySelectorAll('button, a, .feature-card');
        interactiveElements.forEach(el => {
            el.addEventListener('mouseenter', () => {
                this.cursor.style.transform = 'scale(1.5)';
                this.cursor.style.background = 'rgba(255, 255, 255, 0.8)';
            });

            el.addEventListener('mouseleave', () => {
                this.cursor.style.transform = 'scale(1)';
                this.cursor.style.background = 'linear-gradient(45deg, #00ff88, #00ccff)';
            });
        });
    }
}

// 性能优化的滚动监听
class OptimizedScroll {
    constructor() {
        this.ticking = false;
        this.setupOptimizedScroll();
    }

    setupOptimizedScroll() {
        let lastScrollY = 0;
        
        const updateScroll = () => {
            const scrollY = window.pageYOffset;
            
            // 导航栏背景透明度
            const navbar = document.querySelector('.navbar');
            if (scrollY > 50) {
                navbar.style.background = 'rgba(0, 0, 0, 0.95)';
            } else {
                navbar.style.background = 'rgba(0, 0, 0, 0.9)';
            }
            
            lastScrollY = scrollY;
            this.ticking = false;
        };

        window.addEventListener('scroll', () => {
            if (!this.ticking) {
                requestAnimationFrame(updateScroll);
                this.ticking = true;
            }
        });
    }
}

// 初始化所有效果
document.addEventListener('DOMContentLoaded', () => {
    new SilkyScroll();
    new MouseFollower();
    new OptimizedScroll();
    
    // 添加加载动画
    document.body.style.opacity = '0';
    document.body.style.transition = 'opacity 0.5s ease';
    
    setTimeout(() => {
        document.body.style.opacity = '1';
    }, 100);
});

// 防抖函数
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 节流函数
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    }
}
