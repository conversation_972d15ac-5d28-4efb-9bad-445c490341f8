// FlowUI 丝滑滚动效果实现
class FlowUIScroll {
    constructor() {
        this.init();
        this.setupScrollProgress();
        this.setupParallax();
        this.setupScrollAnimations();
        this.setupSmoothScrolling();
        this.setupNavbarEffects();
    }

    init() {
        // 等待DOM加载完成
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.start());
        } else {
            this.start();
        }
    }

    start() {
        console.log('🚀 FlowUI 丝滑滚动效果已启动');
        this.observeElements();
        this.setupIntersectionObserver();
    }

    // 滚动进度条
    setupScrollProgress() {
        const progressBar = document.querySelector('.scroll-progress');
        
        window.addEventListener('scroll', () => {
            const scrollTop = window.pageYOffset;
            const docHeight = document.documentElement.scrollHeight - window.innerHeight;
            const scrollPercent = (scrollTop / docHeight) * 100;
            
            progressBar.style.width = scrollPercent + '%';
        });
    }

    // 视差滚动效果
    setupParallax() {
        const floatingElements = document.querySelectorAll('.float-card');
        const codeWindow = document.querySelector('.code-window');
        const taskBoard = document.querySelector('.task-board');

        window.addEventListener('scroll', () => {
            const scrolled = window.pageYOffset;

            // 浮动卡片视差效果
            floatingElements.forEach((card, index) => {
                const speed = 0.05 + (index * 0.02);
                const rotation = scrolled * 0.005;
                card.style.transform = `translateY(${scrolled * speed}px) rotate(${rotation}deg)`;
            });

            // 代码窗口轻微浮动
            if (codeWindow) {
                const speed = 0.03;
                codeWindow.style.transform = `translateY(${scrolled * speed}px)`;
            }

            // 任务板轻微移动
            if (taskBoard) {
                const speed = 0.02;
                taskBoard.style.transform = `translateY(${scrolled * speed}px)`;
            }
        });
    }

    // 滚动触发动画
    setupScrollAnimations() {
        const animatedElements = document.querySelectorAll(
            '.showcase-item, .testimonial-card, .task-board, .workflow-diagram, .mock-interface'
        );

        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -100px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const element = entry.target;

                    // 添加淡入上升动画
                    element.style.opacity = '1';
                    element.style.transform = 'translateY(0)';
                    element.style.transition = 'all 0.8s cubic-bezier(0.4, 0, 0.2, 1)';

                    // 为不同元素添加延迟效果
                    if (element.classList.contains('testimonial-card')) {
                        const cards = Array.from(element.parentElement.children);
                        const index = cards.indexOf(element);
                        element.style.transitionDelay = `${index * 0.1}s`;
                    }
                }
            });
        }, observerOptions);

        // 初始化元素状态并观察
        animatedElements.forEach(item => {
            item.style.opacity = '0';
            item.style.transform = 'translateY(30px)';
            observer.observe(item);
        });
    }

    // 应用不同的动画效果
    applyAnimation(element, type) {
        switch(type) {
            case 'fadeInLeft':
                element.style.transform = 'translateX(-50px)';
                setTimeout(() => {
                    element.style.transform = 'translateX(0)';
                }, 100);
                break;
            case 'fadeInRight':
                element.style.transform = 'translateX(50px)';
                setTimeout(() => {
                    element.style.transform = 'translateX(0)';
                }, 100);
                break;
            case 'scaleIn':
                element.style.transform = 'scale(0.8)';
                setTimeout(() => {
                    element.style.transform = 'scale(1)';
                }, 100);
                break;
            default:
                // fadeInUp 默认效果
                break;
        }
    }

    // 平滑滚动到锚点
    setupSmoothScrolling() {
        const navLinks = document.querySelectorAll('.nav-menu a[href^="#"]');

        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const targetId = link.getAttribute('href');
                const targetElement = document.querySelector(targetId);

                if (targetElement) {
                    const offsetTop = targetElement.offsetTop - 100; // 考虑导航栏高度

                    window.scrollTo({
                        top: offsetTop,
                        behavior: 'smooth'
                    });
                }
            });
        });
    }

    // 导航栏效果
    setupNavbarEffects() {
        const navbar = document.querySelector('.navbar');
        let lastScrollY = 0;

        window.addEventListener('scroll', () => {
            const scrollY = window.pageYOffset;

            // 滚动时改变导航栏背景
            if (scrollY > 50) {
                navbar.style.background = 'rgba(10, 10, 10, 0.95)';
                navbar.style.borderBottomColor = 'rgba(255, 255, 255, 0.15)';
            } else {
                navbar.style.background = 'rgba(10, 10, 10, 0.8)';
                navbar.style.borderBottomColor = 'rgba(255, 255, 255, 0.1)';
            }

            lastScrollY = scrollY;
        });
    }

    // 设置交叉观察器
    setupIntersectionObserver() {
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-in');
                }
            });
        }, observerOptions);

        // 观察需要动画的元素
        const elementsToObserve = document.querySelectorAll(
            '.hero-badge, .hero-title .title-line, .hero-subtitle, .hero-actions'
        );

        elementsToObserve.forEach(el => observer.observe(el));
    }

    // 观察元素进入视口
    observeElements() {
        // 这个方法现在由 setupIntersectionObserver 处理
    }
}

// 鼠标跟随效果
class MouseFollower {
    constructor() {
        this.cursor = this.createCursor();
        this.setupMouseEvents();
    }

    createCursor() {
        const cursor = document.createElement('div');
        cursor.className = 'custom-cursor';
        cursor.style.cssText = `
            position: fixed;
            width: 20px;
            height: 20px;
            background: linear-gradient(45deg, #00ff88, #00ccff);
            border-radius: 50%;
            pointer-events: none;
            z-index: 9999;
            transition: transform 0.1s ease;
            opacity: 0;
        `;
        document.body.appendChild(cursor);
        return cursor;
    }

    setupMouseEvents() {
        document.addEventListener('mousemove', (e) => {
            this.cursor.style.left = e.clientX - 10 + 'px';
            this.cursor.style.top = e.clientY - 10 + 'px';
            this.cursor.style.opacity = '0.8';
        });

        document.addEventListener('mouseenter', () => {
            this.cursor.style.opacity = '0.8';
        });

        document.addEventListener('mouseleave', () => {
            this.cursor.style.opacity = '0';
        });

        // 悬停效果
        const interactiveElements = document.querySelectorAll('button, a, .feature-card');
        interactiveElements.forEach(el => {
            el.addEventListener('mouseenter', () => {
                this.cursor.style.transform = 'scale(1.5)';
                this.cursor.style.background = 'rgba(255, 255, 255, 0.8)';
            });

            el.addEventListener('mouseleave', () => {
                this.cursor.style.transform = 'scale(1)';
                this.cursor.style.background = 'linear-gradient(45deg, #00ff88, #00ccff)';
            });
        });
    }
}

// 性能优化的滚动监听
class OptimizedScroll {
    constructor() {
        this.ticking = false;
        this.setupOptimizedScroll();
    }

    setupOptimizedScroll() {
        let lastScrollY = 0;
        
        const updateScroll = () => {
            const scrollY = window.pageYOffset;
            
            // 导航栏背景透明度
            const navbar = document.querySelector('.navbar');
            if (scrollY > 50) {
                navbar.style.background = 'rgba(0, 0, 0, 0.95)';
            } else {
                navbar.style.background = 'rgba(0, 0, 0, 0.9)';
            }
            
            lastScrollY = scrollY;
            this.ticking = false;
        };

        window.addEventListener('scroll', () => {
            if (!this.ticking) {
                requestAnimationFrame(updateScroll);
                this.ticking = true;
            }
        });
    }
}

// 定价页面交互
class PricingInteraction {
    constructor() {
        this.setupPricingToggle();
        this.setupPricingAnimations();
    }

    setupPricingToggle() {
        const toggle = document.getElementById('pricing-toggle');
        if (!toggle) return;

        const monthlyElements = document.querySelectorAll('.monthly');
        const yearlyElements = document.querySelectorAll('.yearly');

        toggle.addEventListener('change', () => {
            const isYearly = toggle.checked;

            // 切换显示/隐藏价格
            monthlyElements.forEach(el => {
                el.style.display = isYearly ? 'none' : 'inline';
            });

            yearlyElements.forEach(el => {
                el.style.display = isYearly ? 'inline' : 'none';
            });

            // 添加切换动画
            const pricingCards = document.querySelectorAll('.pricing-card');
            pricingCards.forEach((card, index) => {
                card.style.transform = 'scale(0.95)';
                card.style.opacity = '0.8';

                setTimeout(() => {
                    card.style.transform = '';
                    card.style.opacity = '';
                }, 100 + index * 50);
            });
        });
    }

    setupPricingAnimations() {
        const pricingCards = document.querySelectorAll('.pricing-card');
        const faqItems = document.querySelectorAll('.faq-item');

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const element = entry.target;

                    if (element.classList.contains('pricing-card')) {
                        const cards = Array.from(element.parentElement.children);
                        const index = cards.indexOf(element);

                        setTimeout(() => {
                            element.style.opacity = '1';
                            element.style.transform = 'translateY(0)';
                        }, index * 150);
                    } else if (element.classList.contains('faq-item')) {
                        const items = Array.from(element.parentElement.children);
                        const index = items.indexOf(element);

                        setTimeout(() => {
                            element.style.opacity = '1';
                            element.style.transform = 'translateY(0)';
                        }, index * 100);
                    }
                }
            });
        }, {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        });

        // 初始化动画状态
        [...pricingCards, ...faqItems].forEach(item => {
            item.style.opacity = '0';
            item.style.transform = 'translateY(30px)';
            item.style.transition = 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)';
            observer.observe(item);
        });
    }
}

// 添加按钮交互效果
class ButtonEffects {
    constructor() {
        this.setupButtonEffects();
    }

    setupButtonEffects() {
        const buttons = document.querySelectorAll('.cta-primary, .cta-secondary, .join-waitlist-btn, .pricing-btn');

        buttons.forEach(button => {
            button.addEventListener('click', (e) => {
                // 创建涟漪效果
                const ripple = document.createElement('span');
                const rect = button.getBoundingClientRect();
                const size = Math.max(rect.width, rect.height);
                const x = e.clientX - rect.left - size / 2;
                const y = e.clientY - rect.top - size / 2;

                ripple.style.cssText = `
                    position: absolute;
                    width: ${size}px;
                    height: ${size}px;
                    left: ${x}px;
                    top: ${y}px;
                    background: rgba(255, 255, 255, 0.3);
                    border-radius: 50%;
                    transform: scale(0);
                    animation: ripple 0.6s ease-out;
                    pointer-events: none;
                `;

                button.style.position = 'relative';
                button.style.overflow = 'hidden';
                button.appendChild(ripple);

                setTimeout(() => {
                    ripple.remove();
                }, 600);
            });
        });

        // 添加涟漪动画CSS
        const style = document.createElement('style');
        style.textContent = `
            @keyframes ripple {
                to {
                    transform: scale(2);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);
    }
}

// 初始化所有效果
document.addEventListener('DOMContentLoaded', () => {
    new FlowUIScroll();
    new MouseFollower();
    new OptimizedScroll();
    new PricingInteraction();
    new ButtonEffects();

    // 添加页面加载动画
    document.body.style.opacity = '0';
    document.body.style.transition = 'opacity 0.8s cubic-bezier(0.4, 0, 0.2, 1)';

    setTimeout(() => {
        document.body.style.opacity = '1';
    }, 100);

    // 添加页面加载完成后的效果
    window.addEventListener('load', () => {
        console.log('🎉 FlowUI 页面加载完成');
    });
});

// 防抖函数
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 节流函数
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    }
}
