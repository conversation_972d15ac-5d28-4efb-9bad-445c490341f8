/* 重置和基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    overflow-x: hidden;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #333;
    background: #000;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* 导航栏 */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(10px);
    z-index: 1000;
    transition: all 0.3s ease;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 1rem 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-logo h2 {
    color: #fff;
    font-weight: 700;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-menu a {
    color: #fff;
    text-decoration: none;
    transition: color 0.3s ease;
}

.nav-menu a:hover {
    color: #00ff88;
}

/* 英雄区域 */
.hero {
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 5%;
    background: linear-gradient(135deg, #000 0%, #1a1a1a 100%);
    position: relative;
    overflow: hidden;
}

.hero-content {
    flex: 1;
    max-width: 600px;
}

.hero-title {
    font-size: clamp(3rem, 8vw, 6rem);
    font-weight: 900;
    color: #fff;
    line-height: 1.1;
    margin-bottom: 2rem;
}

.title-line {
    display: block;
    opacity: 0;
    transform: translateY(100px);
    animation: slideInUp 1s ease forwards;
}

.title-line:nth-child(2) {
    animation-delay: 0.2s;
    background: linear-gradient(45deg, #00ff88, #00ccff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.2rem;
    color: #ccc;
    margin-bottom: 3rem;
    opacity: 0;
    animation: fadeInUp 1s ease 0.4s forwards;
}

.cta-button {
    padding: 1rem 2rem;
    font-size: 1.1rem;
    background: linear-gradient(45deg, #00ff88, #00ccff);
    color: #000;
    border: none;
    border-radius: 50px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
    opacity: 0;
    animation: fadeInUp 1s ease 0.6s forwards;
}

.cta-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(0, 255, 136, 0.3);
}

/* 浮动卡片 */
.hero-visual {
    flex: 1;
    position: relative;
    height: 500px;
}

.floating-card {
    position: absolute;
    width: 200px;
    height: 120px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    animation: float 6s ease-in-out infinite;
}

.card-1 {
    top: 50px;
    right: 100px;
    animation-delay: 0s;
}

.card-2 {
    top: 200px;
    right: 300px;
    animation-delay: 2s;
}

.card-3 {
    top: 350px;
    right: 150px;
    animation-delay: 4s;
}

/* 特性区域 */
.features {
    padding: 8rem 0;
    background: #111;
}

.section-title {
    font-size: 3rem;
    color: #fff;
    text-align: center;
    margin-bottom: 4rem;
    font-weight: 700;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 3rem;
    margin-top: 4rem;
}

.feature-card {
    background: rgba(255, 255, 255, 0.05);
    padding: 3rem 2rem;
    border-radius: 20px;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
    transform: translateY(50px);
    opacity: 0;
}

.feature-card:hover {
    transform: translateY(-10px);
    background: rgba(255, 255, 255, 0.1);
}

.feature-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.feature-card h3 {
    color: #fff;
    font-size: 1.5rem;
    margin-bottom: 1rem;
}

.feature-card p {
    color: #ccc;
    line-height: 1.6;
}

/* 展示区域 */
.showcase {
    padding: 8rem 0;
    background: #000;
    overflow: hidden;
}

.showcase-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.showcase-text h2 {
    font-size: 2.5rem;
    color: #fff;
    margin-bottom: 2rem;
}

.showcase-text p {
    color: #ccc;
    margin-bottom: 2rem;
    font-size: 1.1rem;
}

.showcase-text ul {
    list-style: none;
}

.showcase-text li {
    color: #00ff88;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

/* 视差容器 */
.parallax-container {
    position: relative;
    height: 400px;
    border-radius: 20px;
    overflow: hidden;
}

.parallax-layer {
    position: absolute;
    width: 100%;
    height: 120%;
    border-radius: 20px;
}

.layer-1 {
    background: linear-gradient(45deg, #00ff88, #00ccff);
    opacity: 0.3;
}

.layer-2 {
    background: linear-gradient(135deg, #ff0080, #ff8000);
    opacity: 0.2;
    top: 20px;
}

.layer-3 {
    background: linear-gradient(225deg, #8000ff, #0080ff);
    opacity: 0.1;
    top: 40px;
}

/* 滚动动画区域 */
.scroll-animations {
    padding: 8rem 0;
    background: #111;
}

.animation-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 3rem;
    margin-top: 4rem;
}

.anim-item {
    background: rgba(255, 255, 255, 0.05);
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.1);
    opacity: 0;
    transform: translateY(50px);
}

.anim-item h3 {
    color: #fff;
    margin-bottom: 1rem;
}

.anim-item p {
    color: #ccc;
}

/* 联系区域 */
.contact {
    padding: 8rem 0;
    background: #000;
    text-align: center;
}

.contact-subtitle {
    color: #ccc;
    font-size: 1.2rem;
    margin-bottom: 3rem;
}

/* 滚动进度条 */
.scroll-progress {
    position: fixed;
    top: 0;
    left: 0;
    width: 0%;
    height: 3px;
    background: linear-gradient(90deg, #00ff88, #00ccff);
    z-index: 1001;
    transition: width 0.1s ease;
}

/* 动画关键帧 */
@keyframes slideInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(5deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .hero {
        flex-direction: column;
        text-align: center;
        padding: 2rem;
    }
    
    .showcase-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .nav-menu {
        display: none;
    }
}
