/* 重置和基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    overflow-x: hidden;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #e5e5e5;
    background: #0a0a0a;
    overflow-x: hidden;
    font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 24px;
}

/* 渐变文字 */
.gradient-text {
    background: linear-gradient(135deg, #00d4ff 0%, #00ff88 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* 导航栏 */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(10, 10, 10, 0.8);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    z-index: 1000;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 16px 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: 12px;
}

.logo-icon {
    width: 32px;
    height: 32px;
    background: linear-gradient(135deg, #00d4ff 0%, #00ff88 100%);
    border-radius: 8px;
    position: relative;
}

.logo-icon::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 16px;
    height: 16px;
    background: #0a0a0a;
    border-radius: 4px;
}

.logo-text {
    font-size: 20px;
    font-weight: 700;
    color: #ffffff;
    letter-spacing: -0.02em;
}

.preview-badge {
    background: rgba(255, 255, 255, 0.1);
    color: #ffffff;
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 11px;
    font-weight: 600;
    letter-spacing: 0.05em;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 32px;
}

.nav-menu a {
    color: #a1a1aa;
    text-decoration: none;
    font-weight: 500;
    font-size: 14px;
    transition: color 0.2s ease;
}

.nav-menu a:hover {
    color: #ffffff;
}

.join-waitlist-btn {
    background: #ffffff;
    color: #0a0a0a;
    border: none;
    padding: 8px 16px;
    border-radius: 8px;
    font-weight: 600;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.join-waitlist-btn:hover {
    background: #f4f4f5;
    transform: translateY(-1px);
}

/* 英雄区域 */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    padding: 120px 0 80px;
    background: radial-gradient(ellipse at center, rgba(0, 212, 255, 0.05) 0%, transparent 70%);
    position: relative;
    overflow: hidden;
}

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 24px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 80px;
    align-items: center;
}

.hero-badge {
    display: inline-flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 24px;
    padding: 8px 16px;
    margin-bottom: 24px;
    font-size: 14px;
    font-weight: 500;
    color: #a1a1aa;
}

.hero-title {
    font-size: clamp(48px, 6vw, 72px);
    font-weight: 800;
    color: #ffffff;
    line-height: 1.1;
    margin-bottom: 24px;
    letter-spacing: -0.02em;
}

.title-line {
    display: block;
    opacity: 0;
    transform: translateY(30px);
    animation: slideInUp 0.8s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.title-line:nth-child(2) {
    animation-delay: 0.1s;
}

.hero-subtitle {
    font-size: 20px;
    color: #a1a1aa;
    margin-bottom: 40px;
    line-height: 1.6;
    max-width: 480px;
    opacity: 0;
    animation: fadeInUp 0.8s cubic-bezier(0.4, 0, 0.2, 1) 0.2s forwards;
}

.hero-actions {
    display: flex;
    gap: 16px;
    opacity: 0;
    animation: fadeInUp 0.8s cubic-bezier(0.4, 0, 0.2, 1) 0.3s forwards;
}

.cta-primary {
    background: #ffffff;
    color: #0a0a0a;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 600;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.cta-primary:hover {
    background: #f4f4f5;
    transform: translateY(-2px);
}

.cta-primary.large {
    padding: 16px 32px;
    font-size: 18px;
}

.cta-secondary {
    background: transparent;
    color: #ffffff;
    border: 1px solid rgba(255, 255, 255, 0.2);
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 600;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.cta-secondary:hover {
    background: rgba(255, 255, 255, 0.05);
    border-color: rgba(255, 255, 255, 0.3);
}

/* 英雄区域视觉效果 */
.hero-visual {
    position: relative;
    height: 600px;
}

.code-window {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    overflow: hidden;
    backdrop-filter: blur(20px);
    animation: float 6s ease-in-out infinite;
}

.window-header {
    background: rgba(255, 255, 255, 0.05);
    padding: 12px 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.window-controls {
    display: flex;
    gap: 8px;
}

.control {
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.control.red { background: #ff5f57; }
.control.yellow { background: #ffbd2e; }
.control.green { background: #28ca42; }

.window-title {
    color: #a1a1aa;
    font-size: 14px;
    font-weight: 500;
}

.code-content {
    padding: 20px;
    font-family: 'SF Mono', Monaco, 'Cascadia Code', monospace;
    font-size: 14px;
    line-height: 1.6;
}

.code-line {
    margin-bottom: 4px;
}

.keyword { color: #ff79c6; }
.string { color: #f1fa8c; }
.variable { color: #8be9fd; }
.function { color: #50fa7b; }
.method { color: #50fa7b; }

.floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
}

.float-card {
    position: absolute;
    background: rgba(255, 255, 255, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.12);
    border-radius: 12px;
    padding: 16px;
    backdrop-filter: blur(20px);
    display: flex;
    align-items: center;
    gap: 12px;
    animation: float 8s ease-in-out infinite;
}

.card-1 {
    top: 80px;
    right: -20px;
    animation-delay: 0s;
}

.card-2 {
    top: 250px;
    right: 60px;
    animation-delay: 2s;
}

.card-3 {
    top: 420px;
    right: -10px;
    animation-delay: 4s;
}

.card-icon {
    font-size: 20px;
}

.card-text {
    color: #ffffff;
    font-size: 14px;
    font-weight: 500;
    white-space: nowrap;
}

/* 主要功能区域 */
.main-feature {
    padding: 120px 0;
    background: #0a0a0a;
}

.feature-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 80px;
    align-items: center;
    margin-bottom: 80px;
}

.feature-title {
    font-size: clamp(36px, 5vw, 56px);
    font-weight: 700;
    color: #ffffff;
    line-height: 1.2;
    margin-bottom: 24px;
    letter-spacing: -0.02em;
}

.feature-description {
    font-size: 20px;
    color: #a1a1aa;
    line-height: 1.6;
    margin-bottom: 40px;
}

.feature-steps {
    display: flex;
    flex-direction: column;
    gap: 32px;
}

.step {
    display: flex;
    gap: 24px;
    align-items: flex-start;
}

.step-number {
    background: linear-gradient(135deg, #00d4ff 0%, #00ff88 100%);
    color: #0a0a0a;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 16px;
    flex-shrink: 0;
}

.step-content h3 {
    color: #ffffff;
    font-size: 18px;
    font-weight: 600;
    line-height: 1.4;
}

.feature-visual {
    position: relative;
}

.task-board {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    padding: 24px;
    backdrop-filter: blur(20px);
}

.task-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.task-header h4 {
    color: #ffffff;
    font-size: 18px;
    font-weight: 600;
}

.task-progress {
    display: flex;
    align-items: center;
    gap: 12px;
}

.progress-bar {
    width: 120px;
    height: 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    width: 75%;
    height: 100%;
    background: linear-gradient(90deg, #00d4ff, #00ff88);
    border-radius: 4px;
}

.task-progress span {
    color: #a1a1aa;
    font-size: 14px;
    font-weight: 500;
}

.task-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.task-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 0;
}

.task-checkbox {
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 4px;
    position: relative;
    flex-shrink: 0;
}

.task-checkbox.checked {
    background: linear-gradient(135deg, #00d4ff 0%, #00ff88 100%);
    border-color: transparent;
}

.task-checkbox.checked::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #0a0a0a;
    font-size: 12px;
    font-weight: 700;
}

.task-checkbox.progress {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.3);
}

.task-checkbox.progress::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 8px;
    height: 8px;
    background: #00d4ff;
    border-radius: 50%;
}

.task-item span {
    color: #ffffff;
    font-size: 16px;
    font-weight: 500;
}

.task-item.completed span {
    color: #a1a1aa;
    text-decoration: line-through;
}

/* 自动化区域 */
.automation-section {
    padding: 120px 0;
    background: rgba(255, 255, 255, 0.02);
}

.automation-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 80px;
    align-items: center;
}

.automation-text h2 {
    font-size: clamp(36px, 5vw, 48px);
    font-weight: 700;
    color: #ffffff;
    line-height: 1.2;
    margin-bottom: 24px;
    letter-spacing: -0.02em;
}

.automation-text p {
    font-size: 18px;
    color: #a1a1aa;
    line-height: 1.6;
    margin-bottom: 32px;
}

.feature-list {
    list-style: none;
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.feature-list li {
    color: #ffffff;
    font-size: 16px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 12px;
}

.feature-list li::before {
    content: '✓';
    color: #00ff88;
    font-weight: 700;
    font-size: 14px;
}

.automation-visual {
    display: flex;
    justify-content: center;
}

.workflow-diagram {
    display: flex;
    align-items: center;
    gap: 24px;
    padding: 32px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    backdrop-filter: blur(20px);
}

.workflow-node {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
    padding: 20px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    min-width: 100px;
}

.workflow-node.trigger {
    background: rgba(0, 212, 255, 0.1);
    border-color: rgba(0, 212, 255, 0.3);
}

.workflow-node.agent {
    background: rgba(0, 255, 136, 0.1);
    border-color: rgba(0, 255, 136, 0.3);
}

.workflow-node.output {
    background: rgba(255, 136, 0, 0.1);
    border-color: rgba(255, 136, 0, 0.3);
}

.node-icon {
    font-size: 24px;
}

.workflow-node span {
    color: #ffffff;
    font-size: 14px;
    font-weight: 500;
    text-align: center;
}

.workflow-arrow {
    color: #a1a1aa;
    font-size: 20px;
    font-weight: 700;
}

/* 功能展示区域 */
.features-showcase {
    padding: 120px 0;
    background: #0a0a0a;
}

.showcase-header {
    text-align: center;
    margin-bottom: 80px;
}

.section-badge {
    display: inline-block;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 24px;
    padding: 8px 16px;
    margin-bottom: 24px;
    font-size: 14px;
    font-weight: 500;
    color: #a1a1aa;
}

.section-title {
    font-size: clamp(36px, 5vw, 56px);
    font-weight: 700;
    color: #ffffff;
    line-height: 1.2;
    letter-spacing: -0.02em;
    max-width: 800px;
    margin: 0 auto;
}

.showcase-grid {
    display: flex;
    flex-direction: column;
    gap: 120px;
}

.showcase-item {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 80px;
    align-items: center;
}

.showcase-item.reverse {
    direction: rtl;
}

.showcase-item.reverse > * {
    direction: ltr;
}

.showcase-image {
    position: relative;
}

.mock-interface {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    overflow: hidden;
    backdrop-filter: blur(20px);
}

.interface-header {
    background: rgba(255, 255, 255, 0.05);
    padding: 16px 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.header-tabs {
    display: flex;
    gap: 24px;
}

.tab {
    color: #a1a1aa;
    font-size: 14px;
    font-weight: 500;
    padding: 8px 0;
    border-bottom: 2px solid transparent;
    cursor: pointer;
}

.tab.active {
    color: #ffffff;
    border-bottom-color: #00d4ff;
}

.interface-content {
    padding: 24px;
    font-family: 'SF Mono', Monaco, 'Cascadia Code', monospace;
    font-size: 14px;
    line-height: 1.6;
}

.task-row {
    padding: 8px 0;
    color: #a1a1aa;
    display: flex;
    align-items: center;
    gap: 12px;
}

.task-row.completed {
    color: #00ff88;
}

.task-row.in-progress {
    color: #00d4ff;
}

.task-row.pending {
    color: #a1a1aa;
}

.diff-line {
    padding: 4px 0;
    font-family: 'SF Mono', Monaco, 'Cascadia Code', monospace;
}

.diff-line.added {
    color: #00ff88;
    background: rgba(0, 255, 136, 0.1);
    padding-left: 12px;
    margin-left: -12px;
}

.diff-line.removed {
    color: #ff5555;
    background: rgba(255, 85, 85, 0.1);
    padding-left: 12px;
    margin-left: -12px;
    text-decoration: line-through;
}

.file-tree {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.file-item {
    color: #a1a1aa;
    font-size: 14px;
    padding: 4px 0;
}

.file-item.indent {
    padding-left: 20px;
}

.showcase-text h3 {
    font-size: 28px;
    font-weight: 700;
    color: #ffffff;
    line-height: 1.3;
    margin-bottom: 16px;
    letter-spacing: -0.02em;
}

.showcase-text p {
    font-size: 18px;
    color: #a1a1aa;
    line-height: 1.6;
}

/* 用户评价区域 */
.testimonials {
    padding: 120px 0;
    background: rgba(255, 255, 255, 0.02);
}

/* 定价区域 */
.pricing {
    padding: 120px 0;
    background: #0a0a0a;
}

.pricing-header {
    text-align: center;
    margin-bottom: 60px;
}

.pricing-subtitle {
    font-size: 18px;
    color: #a1a1aa;
    margin-top: 16px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.pricing-toggle {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 16px;
    margin-bottom: 60px;
}

.toggle-label {
    color: #a1a1aa;
    font-size: 16px;
    font-weight: 500;
}

.toggle-switch {
    position: relative;
    width: 60px;
    height: 32px;
}

.toggle-input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    transition: all 0.3s ease;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 24px;
    width: 24px;
    left: 3px;
    bottom: 3px;
    background: #ffffff;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.toggle-input:checked + .toggle-slider {
    background: linear-gradient(135deg, #00d4ff 0%, #00ff88 100%);
    border-color: transparent;
}

.toggle-input:checked + .toggle-slider:before {
    transform: translateX(28px);
    background: #0a0a0a;
}

.discount-badge {
    background: linear-gradient(135deg, #00d4ff 0%, #00ff88 100%);
    color: #0a0a0a;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 700;
    margin-left: 8px;
}

.pricing-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 32px;
    margin-bottom: 80px;
}

.pricing-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 32px;
    position: relative;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(20px);
}

.pricing-card:hover {
    transform: translateY(-8px);
    background: rgba(255, 255, 255, 0.08);
    border-color: rgba(255, 255, 255, 0.2);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.pricing-card.popular {
    border-color: rgba(0, 212, 255, 0.5);
    background: rgba(0, 212, 255, 0.05);
    transform: scale(1.05);
}

.pricing-card.popular:hover {
    transform: scale(1.05) translateY(-8px);
}

.popular-badge {
    position: absolute;
    top: -12px;
    left: 50%;
    transform: translateX(-50%);
    background: linear-gradient(135deg, #00d4ff 0%, #00ff88 100%);
    color: #0a0a0a;
    padding: 8px 24px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 700;
    white-space: nowrap;
}

.card-header {
    text-align: center;
    margin-bottom: 32px;
    padding-bottom: 24px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.plan-name {
    color: #ffffff;
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 16px;
}

.plan-price {
    display: flex;
    align-items: baseline;
    justify-content: center;
    margin-bottom: 16px;
}

.currency {
    color: #a1a1aa;
    font-size: 20px;
    font-weight: 600;
}

.amount {
    color: #ffffff;
    font-size: 48px;
    font-weight: 800;
    margin: 0 4px;
}

.period {
    color: #a1a1aa;
    font-size: 16px;
    font-weight: 500;
}

.plan-description {
    color: #a1a1aa;
    font-size: 16px;
    line-height: 1.5;
}

.card-features {
    margin-bottom: 32px;
}

.features-list {
    list-style: none;
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 16px;
    line-height: 1.5;
}

.feature-item.disabled {
    opacity: 0.5;
}

.feature-icon {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: 700;
    flex-shrink: 0;
}

.feature-item:not(.disabled) .feature-icon {
    background: linear-gradient(135deg, #00d4ff 0%, #00ff88 100%);
    color: #0a0a0a;
}

.feature-item.disabled .feature-icon {
    background: rgba(255, 255, 255, 0.1);
    color: #a1a1aa;
}

.feature-item span:last-child {
    color: #ffffff;
}

.feature-item.disabled span:last-child {
    color: #a1a1aa;
}

.card-action {
    text-align: center;
}

.pricing-btn {
    width: 100%;
    padding: 16px 24px;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    border: none;
}

.pricing-btn.primary {
    background: linear-gradient(135deg, #00d4ff 0%, #00ff88 100%);
    color: #0a0a0a;
}

.pricing-btn.primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 212, 255, 0.3);
}

.pricing-btn.secondary {
    background: transparent;
    color: #ffffff;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.pricing-btn.secondary:hover {
    background: rgba(255, 255, 255, 0.05);
    border-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

/* FAQ 部分 */
.pricing-faq {
    text-align: center;
}

.faq-title {
    color: #ffffff;
    font-size: 32px;
    font-weight: 700;
    margin-bottom: 48px;
    letter-spacing: -0.02em;
}

.faq-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 32px;
    text-align: left;
}

.faq-item {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    padding: 24px;
    transition: all 0.3s ease;
}

.faq-item:hover {
    background: rgba(255, 255, 255, 0.08);
    transform: translateY(-4px);
}

.faq-question {
    color: #ffffff;
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 12px;
    line-height: 1.4;
}

.faq-answer {
    color: #a1a1aa;
    font-size: 16px;
    line-height: 1.6;
}

.testimonials .section-title {
    text-align: center;
    margin-bottom: 80px;
}

.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 32px;
}

.testimonial-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    padding: 32px;
    backdrop-filter: blur(20px);
    transition: all 0.3s ease;
}

.testimonial-card:hover {
    background: rgba(255, 255, 255, 0.08);
    transform: translateY(-4px);
}

.testimonial-card p {
    color: #ffffff;
    font-size: 16px;
    line-height: 1.6;
    margin-bottom: 24px;
    font-style: italic;
}

.testimonial-author {
    display: flex;
    align-items: center;
    gap: 16px;
}

.author-avatar {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, #00d4ff 0%, #00ff88 100%);
    border-radius: 50%;
}

.author-name {
    color: #ffffff;
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 4px;
}

.author-title {
    color: #a1a1aa;
    font-size: 14px;
}

/* 最终CTA区域 */
.final-cta {
    padding: 120px 0;
    background: #0a0a0a;
    text-align: center;
}

.cta-content h2 {
    font-size: clamp(36px, 5vw, 48px);
    font-weight: 700;
    color: #ffffff;
    line-height: 1.2;
    margin-bottom: 16px;
    letter-spacing: -0.02em;
}

.cta-content p {
    font-size: 20px;
    color: #a1a1aa;
    margin-bottom: 40px;
}

/* 页脚 */
.footer {
    padding: 80px 0 40px;
    background: rgba(255, 255, 255, 0.02);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-content {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 80px;
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 24px;
}

.footer-links {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 40px;
}

.link-group h4 {
    color: #ffffff;
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 16px;
}

.link-group a {
    display: block;
    color: #a1a1aa;
    text-decoration: none;
    font-size: 14px;
    margin-bottom: 12px;
    transition: color 0.2s ease;
}

.link-group a:hover {
    color: #ffffff;
}

/* 滚动进度条 */
.scroll-progress {
    position: fixed;
    top: 0;
    left: 0;
    width: 0%;
    height: 3px;
    background: linear-gradient(90deg, #00ff88, #00ccff);
    z-index: 1001;
    transition: width 0.1s ease;
}

/* 动画关键帧 */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    50% {
        transform: translateY(-20px) rotate(2deg);
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .hero-container {
        grid-template-columns: 1fr;
        gap: 60px;
        text-align: center;
    }

    .feature-content {
        grid-template-columns: 1fr;
        gap: 60px;
    }

    .automation-content {
        grid-template-columns: 1fr;
        gap: 60px;
    }

    .showcase-item {
        grid-template-columns: 1fr;
        gap: 60px;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 40px;
    }

    .pricing-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 24px;
    }

    .pricing-card.popular {
        transform: none;
    }

    .pricing-card.popular:hover {
        transform: translateY(-8px);
    }
}

@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }

    .join-waitlist-btn {
        display: none;
    }

    .hero {
        padding: 100px 0 60px;
    }

    .hero-container {
        padding: 0 20px;
    }

    .hero-actions {
        flex-direction: column;
        align-items: center;
        gap: 12px;
    }

    .cta-primary,
    .cta-secondary {
        width: 100%;
        max-width: 280px;
        justify-content: center;
    }

    .main-feature,
    .automation-section,
    .features-showcase,
    .testimonials,
    .pricing,
    .final-cta {
        padding: 80px 0;
    }

    .container {
        padding: 0 20px;
    }

    .testimonials-grid {
        grid-template-columns: 1fr;
    }

    .pricing-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .pricing-toggle {
        flex-direction: column;
        gap: 12px;
        text-align: center;
    }

    .faq-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .footer-links {
        grid-template-columns: 1fr;
        gap: 32px;
    }

    .workflow-diagram {
        flex-direction: column;
        gap: 16px;
    }

    .workflow-arrow {
        transform: rotate(90deg);
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 36px;
    }

    .feature-title,
    .section-title {
        font-size: 28px;
    }

    .showcase-text h3 {
        font-size: 24px;
    }

    .task-board,
    .mock-interface {
        margin: 0 -10px;
    }
}
