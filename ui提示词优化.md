请你创建一个JSON格式的设计系统档案，从提供的截图或者提供的参考链接中提取视觉和交互数据。这个JSON将用于Cursor等AI编程工具，为其提供统一风格复现的上下文信息。

请按以下结构输出，避免包含具体图片或参考链接的内容：
{
  "designSystem": {
    "visual": {
      "colorPalette": {
        "philosophy": "极简配色，避免过度饱和",
        "primary": "深色背景(#0a0a0a, #111111)或纯白背景",
        "accent": "低饱和度的品牌色，或纯黑白对比",
        "gradients": "微妙的同色系渐变，或完全避免渐变",
        "avoidance": "避免鲜艳的紫色、粉色等高饱和度颜色"
      },
      "layout": {
        "asymmetry": "打破严格居中，使用不对称布局增加动态感",
        "whitespace": "大胆使用留白，宁可空旷也不拥挤",
        "proportions": "黄金比例和不等分布局",
        "hierarchy": "明确的视觉层次，一个主要焦点"
      },
      "typography": {
        "contrast": "强烈的字重对比(300 vs 700)",
        "sizing": "大胆的字号差异，标题可以很大",
        "color": "高对比度，纯黑或纯白文字",
        "spacing": "充足的字母和行间距"
      }
    },
    "premiumDesignPrinciples": {
      "restraint": {
        "colorUsage": "最多使用2-3种颜色",
        "effects": "避免过多的阴影、渐变、特效",
        "animation": "动画要微妙，不要炫技"
      },
      "sophistication": {
        "brutalism": "考虑粗野主义设计，大胆的几何形状",
        "minimalism": "极简主义，每个元素都有存在的理由",
        "modernism": "现代主义，功能决定形式"
      },
      "authenticity": {
        "avoidTrends": "避免流行但廉价的设计趋势",
        "timeless": "追求经典和永恒的设计",
        "substance": "内容和功能优先于装饰"
      }
    },
    "expensiveLook": {
      "spaciousness": "大量留白，宽松的布局",
      "quality": "高质量的图片和图标",
      "consistency": "完美的对齐和一致性",
      "subtlety": "微妙的细节，而非明显的装饰",
      "confidence": "自信的设计决策，不迎合所有人"
    },
    "avoidCheapLook": {
      "colorMistakes": [
        "避免霓虹色和过饱和颜色",
        "避免彩虹渐变",
        "避免过多颜色混合"
      ],
      "layoutMistakes": [
        "避免所有内容都居中",
        "避免拥挤的布局",
        "避免对称过度"
      ],
      "typographyMistakes": [
        "避免花哨字体",
        "避免过多字重变化",
        "避免彩色文字"
      ]
    }
  }
}

关键原则：
1. **Less is More**: 减少元素，增加质感
2. **高对比度**: 纯黑白对比比彩色更高级
3. **不对称美**: 打破居中，创造动态平衡
4. **留白的力量**: 空间本身就是设计元素
5. **克制的配色**: 最多2-3种颜色，避免彩虹色
6. **功能导向**: 每个设计决策都有功能目的
7. **品质细节**: 完美的对齐、间距、比例