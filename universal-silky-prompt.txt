请基于提供的网站截图/链接，创建一个具有现代化丝滑交互体验的网页复刻版本。严格按照以下规范实现：

🎨 **视觉风格要求**：
- 配色：深色背景(#0a0a0a) + 青绿渐变主色调(linear-gradient(135deg, #00d4ff 0%, #00ff88 100%))
- 玻璃拟态：所有卡片使用 background: rgba(255,255,255,0.05), border: 1px solid rgba(255,255,255,0.1), backdrop-filter: blur(20px)
- 字体：Inter字体族，标题字重700-900，正文400-500，标题字间距-0.02em
- 圆角：按钮8px，卡片16px，容器20px
- 间距：使用8的倍数(8,16,24,32,48,80,120px)

🚀 **丝滑交互动画**：
1. 滚动触发动画：元素初始 opacity: 0, transform: translateY(30px)，进入视口时 transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1) 变为 opacity: 1, transform: translateY(0)
2. 悬停效果：卡片hover时 transform: translateY(-4px)，按钮hover时 transform: translateY(-2px) + box-shadow: 0 8px 25px rgba(0, 212, 255, 0.3)
3. 视差滚动：浮动元素随滚动轻微移动，速度为滚动速度的0.02-0.1倍
4. 按钮涟漪：点击时创建扩散的圆形涟漪效果
5. 渐进加载：相邻元素依次出现，每个延迟150ms

⚡ **性能优化要求**：
- 动画只使用 transform 和 opacity 属性
- 使用 requestAnimationFrame 优化滚动事件
- 用 IntersectionObserver 实现滚动触发动画
- 添加 will-change: transform 给动画元素

📱 **响应式适配**：
- 移动端(<768px)：单列布局，简化动画
- 平板端(768-1024px)：双列布局，保持核心动画
- 桌面端(>1024px)：多列布局，完整交互效果

🎯 **必须实现的核心特性**：
- 固定导航栏，滚动时背景透明度变化
- 滚动进度条(顶部3px高度，渐变色填充)
- 所有按钮的悬停上浮效果
- 卡片的悬停阴影和位移效果
- 页面加载时的淡入动画
- 平滑滚动行为(scroll-behavior: smooth)

💡 **交互细节**：
- 鼠标悬停在交互元素上时，自定义光标变大并发光
- 表单切换(如价格切换)要有平滑的数值变化动画
- 导航链接点击时平滑滚动到对应区域
- 所有状态变化都要有0.2-0.3s的过渡动画

🔧 **代码结构要求**：
- HTML使用语义化标签
- CSS使用现代特性(Grid, Flexbox, CSS变量)
- JavaScript使用ES6+语法，模块化组织
- 添加适当的loading状态和错误处理

请完整实现HTML、CSS、JavaScript三个文件，确保每个交互都丝滑流畅，视觉效果现代化且具有科技感。重点关注动画的流畅性和用户体验的细腻程度。
